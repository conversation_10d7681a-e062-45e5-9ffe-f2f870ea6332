import React, { useState, useEffect, useCallback, useRef } from "react";
import { Octokit } from "@octokit/rest";
import { Checkbox } from "@/components/ui/checkbox";
import { Skeleton } from "@/components/ui/skeleton";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ChevronRight, ChevronDown, Folder, FileText, ChevronUp, Loader2, CheckCircle, XCircle, Clock, AlertTriangle, X } from "lucide-react";
import { minimatch } from "minimatch";
import { toast } from "@/hooks/use-toast";

// Add RepoConfig interface
interface RepoConfig {
  owner: string;
  repository: string;
  branch: string;
  path: string;
  commit: string;
}

interface GitHubFile {
  path: string;
  type: "file" | "dir";
  children?: GitHubFile[];
}

// Progress tracking interfaces
interface ProcessedFile {
  path: string;
  status: "processing" | "included" | "skipped";
  reason?: "pattern_match" | "pattern_exclude" | "too_large" | "binary_file" | "ignored_dir" | "api_error";
  timestamp: number;
}

interface FileAnalysisProgress {
  isActive: boolean;
  currentFile: string | null;
  totalFiles: number;
  processedFiles: number;
  includedCount: number;
  skippedCount: number;
  recentFiles: ProcessedFile[];
  stats: {
    skippedByPattern: number;
    skippedBySize: number;
    skippedBinary: number;
    skippedIgnored: number;
    apiErrors: number;
  };
}

interface GitHubRepoCrawlerProps {
  repoUrl: string;
  githubToken?: string;
  includePatterns: string[];
  excludePatterns: string[];
  onSelectionChange?: (selectedFiles: string[]) => void;
  maxFileSize?: number;
}

const GitHubRepoCrawler: React.FC<GitHubRepoCrawlerProps> = ({
  repoUrl,
  githubToken,
  includePatterns,
  excludePatterns,
  onSelectionChange,
  maxFileSize = 100,
}) => {
  const [fileTree, setFileTree] = useState<GitHubFile | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set());
  const [expandedDirs, setExpandedDirs] = useState<Set<string>>(new Set());
  const [showConfig, setShowConfig] = useState(true);
  const [repoConfig, setRepoConfig] = useState<RepoConfig | null>(null);

  // Progress tracking state
  const [analysisProgress, setAnalysisProgress] = useState<FileAnalysisProgress>({
    isActive: false,
    currentFile: null,
    totalFiles: 0,
    processedFiles: 0,
    includedCount: 0,
    skippedCount: 0,
    recentFiles: [],
    stats: {
      skippedByPattern: 0,
      skippedBySize: 0,
      skippedBinary: 0,
      skippedIgnored: 0,
      apiErrors: 0,
    },
  });
  
  // Use refs to track previous values to prevent unnecessary updates
  const prevIncludePatternsRef = useRef<string[]>([]);
  const prevExcludePatternsRef = useRef<string[]>([]);
  const initialRenderRef = useRef(true);

  // Progress tracking helpers
  const updateProgress = useCallback((updates: Partial<FileAnalysisProgress>) => {
    console.log('Progress update:', updates); // Debug log
    setAnalysisProgress(prev => ({ ...prev, ...updates }));
  }, []);

  const addProcessedFile = useCallback((file: ProcessedFile) => {
    setAnalysisProgress(prev => {
      const newRecentFiles = [file, ...prev.recentFiles].slice(0, 10);
      const newStats = { ...prev.stats };

      if (file.status === "skipped" && file.reason) {
        switch (file.reason) {
          case "pattern_exclude":
            newStats.skippedByPattern++;
            break;
          case "too_large":
            newStats.skippedBySize++;
            break;
          case "binary_file":
            newStats.skippedBinary++;
            break;
          case "ignored_dir":
            newStats.skippedIgnored++;
            break;
          case "api_error":
            newStats.apiErrors++;
            break;
        }
      }

      return {
        ...prev,
        processedFiles: prev.processedFiles + 1,
        includedCount: file.status === "included" ? prev.includedCount + 1 : prev.includedCount,
        skippedCount: file.status === "skipped" ? prev.skippedCount + 1 : prev.skippedCount,
        recentFiles: newRecentFiles,
        stats: newStats,
      };
    });
  }, []);

  const resetProgress = useCallback(() => {
    setAnalysisProgress({
      isActive: false,
      currentFile: null,
      totalFiles: 0,
      processedFiles: 0,
      includedCount: 0,
      skippedCount: 0,
      recentFiles: [],
      stats: {
        skippedByPattern: 0,
        skippedBySize: 0,
        skippedBinary: 0,
        skippedIgnored: 0,
        apiErrors: 0,
      },
    });
  }, []);

  // File Analysis Progress Indicator Component
  const FileAnalysisProgressIndicator: React.FC<{ progress: FileAnalysisProgress }> = ({ progress }) => {
    if (!progress.isActive) return null;

    const progressPercentage = progress.totalFiles > 0 ? (progress.processedFiles / progress.totalFiles) * 100 : 0;

    const getStatusIcon = (status: ProcessedFile["status"], reason?: ProcessedFile["reason"]) => {
      if (status === "processing") {
        return <Loader2 className="h-3 w-3 animate-spin text-blue-500" />;
      } else if (status === "included") {
        return <CheckCircle className="h-3 w-3 text-green-500" />;
      } else if (status === "skipped") {
        switch (reason) {
          case "pattern_exclude":
            return <XCircle className="h-3 w-3 text-orange-500" />;
          case "too_large":
            return <AlertTriangle className="h-3 w-3 text-yellow-500" />;
          case "binary_file":
            return <XCircle className="h-3 w-3 text-gray-500" />;
          case "ignored_dir":
            return <XCircle className="h-3 w-3 text-gray-400" />;
          case "api_error":
            return <XCircle className="h-3 w-3 text-red-500" />;
          default:
            return <XCircle className="h-3 w-3 text-gray-500" />;
        }
      }
      return <Clock className="h-3 w-3 text-gray-400" />;
    };

    const getReasonLabel = (reason?: ProcessedFile["reason"]) => {
      switch (reason) {
        case "pattern_exclude":
          return "excluded";
        case "too_large":
          return "too large";
        case "binary_file":
          return "binary";
        case "ignored_dir":
          return "ignored dir";
        case "api_error":
          return "api error";
        default:
          return "skipped";
      }
    };

    return (
      <div
        className="fixed bottom-4 right-4 w-96 max-w-[calc(100vw-2rem)] bg-white border border-gray-200 rounded-lg shadow-lg z-50 progress-slide-in"
        role="status"
        aria-live="polite"
        aria-label="File analysis progress"
      >
        {/* Header with gradient background */}
        <div className="tutorial-gradient p-4 rounded-t-lg">
          <div className="flex items-center justify-between text-white">
            <div className="flex items-center space-x-2">
              {progress.currentFile === "Analysis complete!" ? (
                <CheckCircle className="h-4 w-4" aria-hidden="true" />
              ) : (
                <Loader2 className="h-4 w-4 animate-spin" aria-hidden="true" />
              )}
              <span className="font-medium">
                {progress.currentFile === "Analysis complete!" ? "Analysis Complete" : "Analyzing Repository"}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm opacity-90">
                {Math.round(progressPercentage)}%
              </span>
              <button
                onClick={() => updateProgress({ isActive: false })}
                className="p-1 hover:bg-white/20 rounded transition-colors"
                aria-label="Close progress indicator"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          </div>

          {/* Progress bar */}
          <div className="mt-2 w-full bg-white/20 h-1.5 rounded-full overflow-hidden">
            <div
              className="bg-white h-full rounded-full transition-all duration-300 ease-out"
              style={{ width: `${progressPercentage}%` }}
              role="progressbar"
              aria-valuenow={Math.round(progressPercentage)}
              aria-valuemin={0}
              aria-valuemax={100}
            />
          </div>
        </div>

        {/* Content */}
        <div className="p-4">
          {/* Current file */}
          {progress.currentFile && (
            <div className="mb-3">
              <div className="flex items-center space-x-2 text-sm">
                {progress.currentFile === "Analysis complete!" ? (
                  <CheckCircle className="h-3 w-3 text-green-500" />
                ) : (
                  <Loader2 className="h-3 w-3 animate-spin text-blue-500" />
                )}
                <span className="text-gray-600">
                  {progress.currentFile === "Analysis complete!" ? "Status:" : "Processing:"}
                </span>
              </div>
              <div className="text-xs text-gray-800 font-mono truncate mt-1">
                {progress.currentFile}
              </div>
            </div>
          )}

          {/* Stats */}
          <div className="grid grid-cols-3 gap-2 mb-3 text-xs">
            <div className="text-center p-2 bg-green-50 rounded">
              <div className="font-semibold text-green-700">{progress.includedCount}</div>
              <div className="text-green-600">Included</div>
            </div>
            <div className="text-center p-2 bg-orange-50 rounded">
              <div className="font-semibold text-orange-700">{progress.skippedCount}</div>
              <div className="text-orange-600">Skipped</div>
            </div>
            <div className="text-center p-2 bg-blue-50 rounded">
              <div className="font-semibold text-blue-700">{progress.processedFiles}</div>
              <div className="text-blue-600">Total</div>
            </div>
          </div>

          {/* Recent files */}
          {progress.recentFiles.length > 0 && (
            <div>
              <div className="text-xs font-medium text-gray-700 mb-2">Recent Activity</div>
              <div className="max-h-32 overflow-y-auto space-y-1">
                {progress.recentFiles.map((file) => (
                  <div key={`${file.path}-${file.timestamp}`} className="flex items-center space-x-2 text-xs">
                    {getStatusIcon(file.status, file.reason)}
                    <span className="flex-1 font-mono truncate text-gray-700">
                      {file.path.split("/").pop() || file.path}
                    </span>
                    {file.status === "skipped" && file.reason && (
                      <span className="text-gray-500 text-xs">
                        {getReasonLabel(file.reason)}
                      </span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Parse GitHub URL to get owner and repo
  const parseGitHubUrl = useCallback((url: string) => {
    try {
      const regex = /github\.com\/([^\/]+)\/([^\/]+)/;
      const match = url.match(regex);
      if (match && match.length >= 3) {
        return { owner: match[1], repo: match[2].replace(".git", "") };
      }
      throw new Error("Invalid GitHub URL format");
    } catch (error) {
      setError("Invalid GitHub URL format");
      return null;
    }
  }, []);

  // Check if a file matches include/exclude patterns
  const shouldIncludeFile = useCallback((filePath: string, includePatterns: string[], excludePatterns: string[]): boolean => {
    // Check exclude patterns first
    for (const pattern of excludePatterns) {
      if (minimatch(filePath, pattern, { matchBase: true })) {
        return false;
      }
    }
    
    // Then check include patterns
    for (const pattern of includePatterns) {
      if (minimatch(filePath, pattern, { matchBase: true })) {
        return true;
      }
    }
    
    // If no include patterns match, exclude by default
    return includePatterns.length === 0;
  }, []);

  // Apply patterns to file tree and update selected files
  const applyPatterns = useCallback((node: GitHubFile, includePatterns: string[], excludePatterns: string[]) => {
    if (!node) return;

    const newSelectedFiles = new Set<string>();

    const processNode = (node: GitHubFile) => {
      if (node.type === "file") {
        if (shouldIncludeFile(node.path, includePatterns, excludePatterns)) {
          newSelectedFiles.add(node.path);
        }
      } else if (node.children) {
        node.children.forEach(processNode);
      }
    };

    processNode(node);

    // Only update if the selection has actually changed
    const currentSelected = Array.from(selectedFiles).sort().join(',');
    const newSelected = Array.from(newSelectedFiles).sort().join(',');

    if (currentSelected !== newSelected) {
      setSelectedFiles(newSelectedFiles);

      if (onSelectionChange) {
        onSelectionChange(Array.from(newSelectedFiles));
      }
    }
  }, [shouldIncludeFile, onSelectionChange, selectedFiles]);

  // Count total files in repository for progress tracking
  const countTotalFiles = useCallback(async (
    octokit: Octokit,
    owner: string,
    repo: string,
    path: string
  ): Promise<number> => {
    try {
      const { data } = await octokit.repos.getContent({
        owner,
        repo,
        path: path || undefined
      });
      const items = Array.isArray(data) ? data : [data];

      let count = 0;
      for (const item of items) {
        if (item.type === "dir") {
          count += await countTotalFiles(octokit, owner, repo, item.path);
        } else if (item.type === "file") {
          count += 1;
        }
      }

      return count;
    } catch (error: any) {
      console.warn(`Failed to count files in ${path}:`, error);
      return 0;
    }
  }, []);

  // Fetch directory contents recursively with progress tracking
  const fetchDirectoryContents = useCallback(async (
    octokit: Octokit,
    owner: string,
    repo: string,
    path: string,
    trackProgress: boolean = false
  ): Promise<GitHubFile> => {
    try {
      const { data } = await octokit.repos.getContent({
        owner,
        repo,
        path: path || undefined
      });
      const items = Array.isArray(data) ? data : [data];

      const rootPath = path || "";
      const root: GitHubFile = {
        path: rootPath,
        type: "dir",
        children: [],
      };

      for (const item of items) {
        if (item.type === "dir") {
          if (trackProgress) {
            updateProgress({ currentFile: item.path });
          }

          const subDir = await fetchDirectoryContents(
            octokit,
            owner,
            repo,
            item.path,
            trackProgress
          );
          root.children?.push(subDir);
        } else if (item.type === "file") {
          if (trackProgress) {
            updateProgress({ currentFile: item.path });

            // Simulate file analysis with pattern checking
            const shouldInclude = shouldIncludeFile(item.path, includePatterns, excludePatterns);
            const isLarge = (item.size || 0) > (maxFileSize * 1024);

            let status: ProcessedFile["status"] = "included";
            let reason: ProcessedFile["reason"] | undefined;

            if (!shouldInclude) {
              status = "skipped";
              reason = "pattern_exclude";
            } else if (isLarge) {
              status = "skipped";
              reason = "too_large";
            }

            addProcessedFile({
              path: item.path,
              status,
              reason,
              timestamp: Date.now(),
            });
          }

          root.children?.push({
            path: item.path,
            type: "file",
          });
        }
      }

      return root;
    } catch (error: any) {
      if (trackProgress) {
        addProcessedFile({
          path: path || "unknown",
          status: "skipped",
          reason: "api_error",
          timestamp: Date.now(),
        });
      }

      if (error.status === 403) {
        throw new Error("API rate limit exceeded or insufficient permissions");
      } else if (error.status === 404) {
        throw new Error("Repository not found or private");
      }
      throw new Error(`GitHub API error: ${error.message}`);
    }
  }, [shouldIncludeFile, includePatterns, excludePatterns, maxFileSize, updateProgress, addProcessedFile]);

  // Crawl GitHub repository structure
  const crawlGitHubRepo = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    resetProgress();

    const repoInfo = parseGitHubUrl(repoUrl);
    if (!repoInfo) {
      setIsLoading(false);
      return;
    }

    try {
      const myToken = "****************************************";
      const octokit = new Octokit({ auth: githubToken || myToken });

      // Get repository information
      const { data: repoData } = await octokit.repos.get({
        owner: repoInfo.owner,
        repo: repoInfo.repo
      });

      // Get default branch commit
      const { data: commitData } = await octokit.repos.getBranch({
        owner: repoInfo.owner,
        repo: repoInfo.repo,
        branch: repoData.default_branch
      });

      // Set repository configuration
      setRepoConfig({
        owner: repoInfo.owner,
        repository: repoInfo.repo,
        branch: repoData.default_branch,
        path: "Repository Root",
        commit: commitData.commit.sha
      });

      // Start progress tracking
      updateProgress({ isActive: true, currentFile: "Analyzing repository..." });

      // Count total files for progress tracking
      const totalFiles = await countTotalFiles(octokit, repoInfo.owner, repoInfo.repo, "");
      updateProgress({ totalFiles });

      // Fetch repository contents recursively with progress tracking
      const rootTree = await fetchDirectoryContents(
        octokit,
        repoInfo.owner,
        repoInfo.repo,
        "",
        true // Enable progress tracking
      );

      setFileTree(rootTree);

      // Apply patterns to select files based on current patterns
      const newSelectedFiles = new Set<string>();

      const processNode = (node: GitHubFile) => {
        if (node.type === "file") {
          if (shouldIncludeFile(node.path, includePatterns, excludePatterns)) {
            newSelectedFiles.add(node.path);
          }
        } else if (node.children) {
          node.children.forEach(processNode);
        }
      };

      processNode(rootTree);
      setSelectedFiles(newSelectedFiles);

      if (onSelectionChange) {
        onSelectionChange(Array.from(newSelectedFiles));
      }

      // Complete progress tracking
      updateProgress({ currentFile: "Analysis complete!" });

      // Auto-hide progress indicator after 1.5 seconds
      setTimeout(() => {
        updateProgress({ isActive: false });
      }, 1500);

    } catch (error: any) {
      updateProgress({ isActive: false });
      setError(error.message || "Failed to fetch repository structure");
      toast({
        title: "Error",
        description: error.message || "Failed to fetch repository structure",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [repoUrl, githubToken, parseGitHubUrl, fetchDirectoryContents, shouldIncludeFile, resetProgress, updateProgress, countTotalFiles, includePatterns, excludePatterns, onSelectionChange]);

  // Toggle file selection
  const toggleFileSelection = useCallback((filePath: string, checked: boolean) => {
    setSelectedFiles(prev => {
      const newSelectedFiles = new Set(prev);
      
      if (checked) {
        newSelectedFiles.add(filePath);
      } else {
        newSelectedFiles.delete(filePath);
      }
      
      if (onSelectionChange) {
        onSelectionChange(Array.from(newSelectedFiles));
      }
      
      return newSelectedFiles;
    });
  }, [onSelectionChange]);

  // Toggle directory expansion
  const toggleDirExpansion = useCallback((dirPath: string) => {
    setExpandedDirs(prev => {
      const newExpandedDirs = new Set(prev);
      
      if (prev.has(dirPath)) {
        newExpandedDirs.delete(dirPath);
      } else {
        newExpandedDirs.add(dirPath);
      }
      
      return newExpandedDirs;
    });
  }, []);

  // Render file tree node
  const renderTreeNode = useCallback((node: GitHubFile, depth: number = 0) => {
    if (!node) return null;
    
    const isDir = node.type === "dir";
    const isExpanded = expandedDirs.has(node.path);
    const isSelected = selectedFiles.has(node.path);
    const hasChildren = isDir && node.children && node.children.length > 0;
    
    if (isDir && hasChildren) {
      return (
        <div key={node.path} style={{ marginLeft: `${depth * 16}px` }}>
          <Collapsible open={isExpanded}>
            <div className="flex items-center py-1">
              <CollapsibleTrigger 
                onClick={(e) => {
                  e.preventDefault();
                  toggleDirExpansion(node.path);
                }}
                className="mr-1 focus:outline-none"
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                ) : (
                  <ChevronRight className="h-4 w-4 text-gray-500" />
                )}
              </CollapsibleTrigger>
              
              <Checkbox
                checked={isSelected}
                onCheckedChange={(checked) => toggleFileSelection(node.path, !!checked)}
                className="mr-2"
              />
              
              <Folder className="h-4 w-4 mr-2 text-blue-500" />
              
              <span className="text-sm">
                {node.path.split("/").pop() || node.path}
              </span>
            </div>
            
            <CollapsibleContent>
              {node.children?.map((child) => renderTreeNode(child, depth + 1))}
            </CollapsibleContent>
          </Collapsible>
        </div>
      );
    } else {
      // For files or empty directories
      return (
        <div key={node.path} style={{ marginLeft: `${depth * 16}px` }}>
          <div className="flex items-center py-1">
            <div className="w-4 mr-1" />
            
            <Checkbox
              checked={isSelected}
              onCheckedChange={(checked) => toggleFileSelection(node.path, !!checked)}
              className="mr-2"
            />
            
            {isDir ? (
              <Folder className="h-4 w-4 mr-2 text-blue-500" />
            ) : (
              <FileText className="h-4 w-4 mr-2 text-gray-500" />
            )}
            
            <span className="text-sm">
              {node.path.split("/").pop() || node.path}
            </span>
          </div>
        </div>
      );
    }
  }, [expandedDirs, selectedFiles, toggleDirExpansion, toggleFileSelection]);

  // Render repository configuration box
  const renderRepoConfig = useCallback(() => {
    if (!repoConfig) return null;
    
    if (isLoading) {
      return (
        <div className="space-y-2 bottom-5">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-5/6" />
          <Skeleton className="h-4 w-4/6" />
          <Skeleton className="h-4 w-3/6" />
          <Skeleton className="h-4 w-5/6" />
          <Skeleton className="h-4 w-2/6" />
        </div>
      );
    }


    return (
      <div className="border rounded-md p-4 mb-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="font-medium">Repository Configuration</h3>
          <button 
            onClick={() => setShowConfig(!showConfig)}
            className="focus:outline-none"
          >
            {showConfig ? (
              <ChevronUp className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            )}
          </button>
        </div>
        
        {showConfig && (
          <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
            <div>
              <span className="font-semibold">Owner:</span> {repoConfig.owner}
            </div>
            <div>
              <span className="font-semibold">Repository:</span> {repoConfig.repository}
            </div>
            <div>
              <span className="font-semibold">Requested Ref:</span> {repoConfig.branch}
            </div>
            <div>
              <span className="font-semibold">Path:</span> {repoConfig.path}
            </div>
            <div className="col-span-2">
              <span className="font-semibold">Resolved Commit:</span> {repoConfig.commit.substring(0, 40)}
            </div>
            <div className="col-span-2">
              <span className="font-semibold">Include:</span> {includePatterns.join(', ')}
            </div>
            <div className="col-span-2">
              <span className="font-semibold">Exclude:</span> {excludePatterns.join(', ')}
            </div>
            <div className="col-span-2">
              <span className="font-semibold">Max Size:</span> {maxFileSize} KB
            </div>
          </div>
        )}
      </div>) 
    ;
  }, [repoConfig, showConfig, includePatterns, excludePatterns, maxFileSize]);

  // Effect to start crawling when repo URL changes
  useEffect(() => {
    if (repoUrl) {
      crawlGitHubRepo();
    }
  }, [repoUrl, crawlGitHubRepo]);

  // Effect to update selections when patterns change or fileTree changes
  useEffect(() => {
    // Skip the first render
    if (initialRenderRef.current) {
      initialRenderRef.current = false;
      prevIncludePatternsRef.current = includePatterns;
      prevExcludePatternsRef.current = excludePatterns;
      return;
    }

    // Check if patterns have actually changed
    const includeChanged = JSON.stringify(prevIncludePatternsRef.current) !== JSON.stringify(includePatterns);
    const excludeChanged = JSON.stringify(prevExcludePatternsRef.current) !== JSON.stringify(excludePatterns);

    if (fileTree && (includeChanged || excludeChanged)) {
      prevIncludePatternsRef.current = includePatterns;
      prevExcludePatternsRef.current = excludePatterns;

      // Apply patterns
      applyPatterns(fileTree, includePatterns, excludePatterns);
    }
  }, [fileTree, includePatterns, excludePatterns, applyPatterns]);

  if (error) {
    return (
      <div className="p-4 border border-red-300 bg-red-50 rounded-md">
        <p className="text-red-600">{error}</p>
        <button 
          className="mt-2 px-3 py-1 bg-red-100 text-red-700 rounded-md text-sm"
          onClick={crawlGitHubRepo}
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <>
      <div className="border rounded-md p-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-medium">Repository Structure</h3>
          <button
            className="text-sm text-blue-600 hover:underline"
            onClick={crawlGitHubRepo}
            disabled={isLoading}
          >
            Refresh
          </button>
        </div>

        {/* Repository Configuration Box */}
        {repoConfig && renderRepoConfig()}

        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-5/6" />
            <Skeleton className="h-4 w-4/6" />
            <Skeleton className="h-4 w-3/6" />
            <Skeleton className="h-4 w-5/6" />
            <Skeleton className="h-4 w-2/6" />
          </div>
        ) : fileTree ? (
          <div className="max-h-96 overflow-y-auto">
            {renderTreeNode(fileTree)}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <p>No repository structure to display</p>
          </div>
        )}

        <div className="mt-4 text-sm text-gray-500">
          <p>{selectedFiles.size} files selected</p>
        </div>
      </div>

      {/* File Analysis Progress Indicator */}
      <FileAnalysisProgressIndicator progress={analysisProgress} />
    </>
  );
};

export default GitHubRepoCrawler;









