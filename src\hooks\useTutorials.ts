import { useEffect, useState } from "react";
import { Database } from "@/integrations/supabase/types";
import { supabase } from "@/integrations/supabase/client";

export type Tutorial = {
  id: string;
  title: string;
  description: string;
  repoUrl: string;
  createdAt: string;
  tags: string[];
  chaptersCount: number;
  difficulty?: string;
  language?: string;
  views?: number;
  featured?: boolean;
  imageSrc?: string;
  backgroundColor?: string;
  isPublic?: boolean;
};

export const useTutorials = () => {
  const [tutorials, setTutorials] = useState<Tutorial[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTutorials = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from("tutorial_metadata")
          .select("*")
          .order("created_at", { ascending: false });

        if (error) throw new Error(error.message);
        
        const formattedTutorials = data.map(formatTutorialData);
        setTutorials(formattedTutorials);
      } catch (err) {
        console.error("Error fetching tutorials:", err);
        setError(err instanceof Error ? err.message : "Failed to fetch tutorials");
      } finally {
        setLoading(false);
      }
    };

    fetchTutorials();
  }, []); // Added missing dependency array

  return { tutorials, loading, error };
};

export const useFeaturedTutorials = () => {
  const [tutorials, setTutorials] = useState<Tutorial[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);


  useEffect(() => {
    const fetchTutorials = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from("tutorial_metadata")
          .select("*")
          .eq("featured", true)
          //.eq("is_public", true)
          .limit(3);

        if (error) throw new Error(error.message);
        
        const formattedTutorials = data.map(formatTutorialData);
        setTutorials(formattedTutorials);
      } catch (err) {
        console.error("Error fetching featured tutorials:", err);
        setError(err instanceof Error ? err.message : "Failed to fetch tutorials");
      } finally {
        setLoading(false);
      }
    };

    fetchTutorials();
  }, []);

  return { tutorials, loading, error };
};

export const useRecentTutorials = () => {
  const [tutorials, setTutorials] = useState<Tutorial[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);


  useEffect(() => {
    const fetchTutorials = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from("tutorial_metadata")
          .select("*")
         // .eq("is_public", true)
          .order("created_at", { ascending: false })
          .limit(4);

        if (error) throw new Error(error.message);
        
        const formattedTutorials = data.map(formatTutorialData);
        setTutorials(formattedTutorials);
      } catch (err) {
        console.error("Error fetching recent tutorials:", err);
        setError(err instanceof Error ? err.message : "Failed to fetch tutorials");
      } finally {
        setLoading(false);
      }
    };

    fetchTutorials();
  }, []);

  return { tutorials, loading, error };
};

export const usePopularTutorials = () => {
  const [tutorials, setTutorials] = useState<Tutorial[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  

  useEffect(() => {
    const fetchTutorials = async () => {
      try {
        setLoading(true);
        const { data, error } = await supabase
          .from("tutorial_metadata")
          .select("*")
       //   .eq("is_public", true)
          .order("views", { ascending: false })
          .limit(6);

        if (error) throw new Error(error.message);
        
        const formattedTutorials = data.map(formatTutorialData);
        setTutorials(formattedTutorials);
      } catch (err) {
        console.error("Error fetching popular tutorials:", err);
        setError(err instanceof Error ? err.message : "Failed to fetch tutorials");
      } finally {
        setLoading(false);
      }
    };

    fetchTutorials();
  }, []);

  return { tutorials, loading, error };
};

// Helper function to format tutorial data from Supabase to the format used in the UI
const formatTutorialData = (tutorial: Database["public"]["Tables"]["tutorial_metadata"]["Row"]): Tutorial => {
  return {
    id: tutorial.id,
    title: tutorial.project_name,
    description: tutorial.description || "No description available",
    repoUrl: tutorial.repo_url || "",
    createdAt: tutorial.created_at,
    tags: tutorial.tags || [],
    chaptersCount: Array.isArray(tutorial.chapter_urls) ? tutorial.chapter_urls.length : 
      (typeof tutorial.chapter_urls === 'object' ? Object.keys(tutorial.chapter_urls).length : 0),
    difficulty: tutorial.difficulty || "Intermediate",
    language: tutorial.language || "Unknown",
    views: tutorial.views || 0,
    featured: tutorial.featured || false,
    imageSrc: tutorial.cover_url || "https://storage.googleapis.com/uxpilot-auth.appspot.com/placeholder.svg",
    backgroundColor: tutorial.background_color || "from-blue-500 to-indigo-600",
    isPublic: tutorial.is_public || false
  };
};
