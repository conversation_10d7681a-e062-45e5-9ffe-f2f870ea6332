
import { Link } from "react-router-dom";
import AuthButtons from "../auth/AuthButtons";

export function Header() {
  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-10">
      <div className="container mx-auto px-4 py-3 flex items-center justify-between">

        <div className="flex items-center">
          <Link to="/" className="flex items-center cursor-pointer">
            <i className="fa-solid fa-book-open text-primary text-2xl mr-2"></i>
            <span className="text-xl font-bold text-gray-800">CodeTutor</span>
          </Link>
        </div>
        
        <div className="flex items-center space-x-4">
          <a
            href="#pricing"
            className="text-gray-600 hover:text-primary font-medium"
          >
            Pricing
          </a>
          <Link
            to="/public-gallery"
            className="text-gray-600 hover:text-primary font-medium"
          >
            Gallery
          </Link>
          <AuthButtons />
        </div>
      </div>
    </header>
  );
}
