
import { useAuth } from "@/hooks/useAuth";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import UserMenu from "../UserMenu";

const AuthButtons = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return <div className="animate-pulse h-10 w-20 bg-gray-200 rounded"></div>;
  }

  if (user) {
    return <UserMenu />;
  }

  return (
    <div className="flex items-center space-x-2">
      {/* <Link to="/auth">
        <Button variant="outline">Sign In</Button>
      </Link> */}
      <Link to="/auth">
        <Button>Get Started</Button>
      </Link>
    </div>
  );
};

export default AuthButtons;
