
import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const SettingsForm = () => {
  return (
    <Card className="w-full max-w-3xl">
      <CardHeader>
        <CardTitle>Settings</CardTitle>
        <CardDescription>
          Configure parameters for tutorial generation
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="space-y-4">
            <h3 className="text-lg font-medium">File Processing</h3>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="maxFileSize">Maximum File Size (KB)</Label>
                <Input id="maxFileSize" type="number" defaultValue={500} />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="model">LLM Model</Label>
                <Select defaultValue="claude-3-sonnet">
                  <SelectTrigger id="model">
                    <SelectValue placeholder="Select model" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="claude-3-sonnet">Claude 3.7 Sonnet</SelectItem>
                    <SelectItem value="gpt-4o">GPT-4o</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-medium">GitHub Integration</h3>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="githubToken">GitHub Personal Access Token</Label>
                <Input
                  id="githubToken"
                  type="password"
                  placeholder="Enter GitHub token for private repositories"
                />
                <p className="text-xs text-muted-foreground">
                  Required for accessing private GitHub repositories
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-lg font-medium">Display Preferences</h3>
            <div className="grid gap-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="generateDiagrams">Generate Diagrams</Label>
                  <p className="text-xs text-muted-foreground">
                    Include architecture diagrams in tutorials
                  </p>
                </div>
                <Switch id="generateDiagrams" defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="cacheResults">Cache Results</Label>
                  <p className="text-xs text-muted-foreground">
                    Save generated tutorials to improve performance
                  </p>
                </div>
                <Switch id="cacheResults" defaultChecked />
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end gap-3">
        <Button variant="outline">Restore Defaults</Button>
        <Button className="tutorial-gradient">Save Settings</Button>
      </CardFooter>
    </Card>
  );
};

export default SettingsForm;
