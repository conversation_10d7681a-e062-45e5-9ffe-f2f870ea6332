import { callL<PERSON>_openrouter } from "@/Agents/shared/callLlm_openrouter";
import { BatchNode } from "../../../pocketflow";
import { SharedStore } from "../types";

import { emitGraphStatus, emitProgress } from "../utils/events";
import { buildPrompt } from "../../../pocketflow/utils/buildPrompt";
import { WRITE_CHAPTER_PROMPT } from "../prompts/writeChapters";

interface AbstractionDetail {
  name: string;
  description: string;
  files?: string[] | number[]; // Can be file paths or indices
}

interface ChapterItem {
  chapter_num: number;
  abstraction_index: number;
  abstraction_details: AbstractionDetail;
  related_files_content_map: Record<string, string>;
  project_name: string;
  full_chapter_listing: string;
  chapter_filenames: Record<
    number,
    { num: number; name: string; filename: string }
  >;
  prev_chapter: { num: number; name: string; filename: string } | null;
  next_chapter: { num: number; name: string; filename: string } | null;
  language: string;
  use_cache: boolean;
}

export class WriteChapters extends BatchNode<SharedStore> {
  private chapters_written_so_far: string[] = [];
  private curRetry = 0;
 private user_id: string | null = null;

  async prep(shared: SharedStore) {
    // Emit graph status to indicate this node is starting
    emitGraphStatus("WriteChapters", 0, "Starting chapter writing");


    this.user_id = shared.user_id;
    const chapter_order = shared.chapter_order || [];
    const abstractions = shared.abstractions || [];
    const files_data = shared.files || [];
    const project_name = shared.project_name!;
    const language = shared.language || "english";
    const use_cache = shared.use_cache ?? true;

    // Reset temporary storage
    this.chapters_written_so_far = [];

    emitGraphStatus(
      "WriteChapters",
      10,
      `Preparing to write ${chapter_order.length} chapters`
    );

    // Create a complete list of all chapters
    const all_chapters: string[] = [];
    const chapter_filenames: Record<
      number,
      { num: number; name: string; filename: string }
    > = {};

    emitGraphStatus(
      "WriteChapters",
      20,
      "Creating chapter structure and filenames"
    );

    for (let i = 0; i < chapter_order.length; i++) {
      const abstraction_index = chapter_order[i];
      if (0 <= abstraction_index && abstraction_index < abstractions.length) {
        const chapter_num = i + 1;
        const chapter_name = abstractions[abstraction_index].name;

        // Create safe filename
        const safe_name = chapter_name
          .replace(/[^a-zA-Z0-9]/g, "_")
          .toLowerCase();
        const filename = `${(i + 1)
          .toString()
          .padStart(2, "0")}_${safe_name}.md`;

        // Format with link
        all_chapters.push(`${chapter_num}. [${chapter_name}](${filename})`);

        // Store mapping
        chapter_filenames[abstraction_index] = {
          num: chapter_num,
          name: chapter_name,
          filename,
        };
      }
    }

    // Create a formatted string with all chapters
    const full_chapter_listing = all_chapters.join("\n");
    console.log("Full chapter listing:\n", full_chapter_listing);

    emitGraphStatus(
      "WriteChapters",
      30,
      "Gathering file content for each chapter"
    );

    const items_to_process: ChapterItem[] = [];
    for (let i = 0; i < chapter_order.length; i++) {
      const abstraction_index = chapter_order[i];
      if (0 <= abstraction_index && abstraction_index < abstractions.length) {
        const abstraction_details = abstractions[abstraction_index];
        const related_file_indices = abstraction_details.files || [];

        // Get content for indices
        const related_files_content_map: Record<string, string> = {};
        for (const idx of related_file_indices) {
          if (idx >= 0 && idx < files_data.length) {
            const [path, content] = files_data[idx];
            related_files_content_map[`${idx} # ${path}`] = content;
          }
        }

        // Get previous chapter info
        let prev_chapter = null;
        if (i > 0) {
          const prev_idx = chapter_order[i - 1];
          prev_chapter = chapter_filenames[prev_idx];
        }

        // Get next chapter info
        let next_chapter = null;
        if (i < chapter_order.length - 1) {
          const next_idx = chapter_order[i + 1];
          next_chapter = chapter_filenames[next_idx];
        }

        items_to_process.push({
          chapter_num: i + 1,
          abstraction_index,
          abstraction_details,
          related_files_content_map,
          project_name,
          full_chapter_listing,
          chapter_filenames,
          prev_chapter,
          next_chapter,
          language,
          use_cache,
        });
      } else {
        emitGraphStatus(
          "WriteChapters",
          35,
          `Warning: Invalid abstraction index ${abstraction_index} in chapter_order`
        );
        console.log(
          `Warning: Invalid abstraction index ${abstraction_index} in chapter_order. Skipping.`
        );
      }
    }

    emitGraphStatus(
      "WriteChapters",
      40,
      `Preparation complete, ready to write ${items_to_process.length} chapters`
    );
    console.log(`Preparing to write ${items_to_process.length} chapters...`);
    return items_to_process;
  }



  async exec(item: ChapterItem): Promise<string> {
    const abstraction_name = item.abstraction_details.name;
    const abstraction_description = item.abstraction_details.description;
    const chapter_num = item.chapter_num;
    const project_name = item.project_name;
    const language = item.language;
    const use_cache = item.use_cache;

    // Calculate progress percentage for this chapter
    const progressPercentage = Math.floor(
      40 + ((chapter_num - 1) / Object.keys(item.chapter_filenames).length) * 50
    );

    emitGraphStatus(
      "WriteChapters",
      progressPercentage,
      `Writing chapter ${chapter_num}: ${abstraction_name}`
    );

    // Prepare file context string
    emitGraphStatus(
      "WriteChapters",
      progressPercentage + 1,
      `Preparing code snippets for chapter ${chapter_num}`
    );
    const file_context_str = Object.entries(item.related_files_content_map)
      .map(([idx_path, content]) => {
        const path = idx_path.includes("# ")
          ? idx_path.split("# ")[1]
          : idx_path;
        return `--- File: ${path} ---\n${content}`;
      })
      .join("\n\n");

    // Get summary of chapters written before this one
    const previous_chapters_summary =
      this.chapters_written_so_far.join("\n---\n");

    // Add language instruction and context notes only if not English
    let language_instruction = "";
    let concept_details_note = "";
    let structure_note = "";
    let prev_summary_note = "";
    let instruction_lang_note = "";
    let mermaid_lang_note = "";
    let code_comment_note = "";
    let link_lang_note = "";
    let tone_note = "";

    if (language.toLowerCase() !== "english") {
      const lang_cap = language.charAt(0).toUpperCase() + language.slice(1);
      language_instruction = `IMPORTANT: Write this ENTIRE tutorial chapter in **${lang_cap}**. Some input context (like concept name, description, chapter list, previous summary) might already be in ${lang_cap}, but you MUST translate ALL other generated content including explanations, examples, technical terms, and potentially code comments into ${lang_cap}. DO NOT use English anywhere except in code syntax, required proper nouns, or when specified. The entire output MUST be in ${lang_cap}.\n\n`;
      concept_details_note = ` (Note: Provided in ${lang_cap})`;
      structure_note = ` (Note: Chapter names might be in ${lang_cap})`;
      prev_summary_note = ` (Note: This summary might be in ${lang_cap})`;
      instruction_lang_note = ` (in ${lang_cap})`;
      mermaid_lang_note = ` (Use ${lang_cap} for labels/text if appropriate)`;
      code_comment_note = ` (Translate to ${lang_cap} if possible, otherwise keep minimal English for clarity)`;
      link_lang_note = ` (Use the ${lang_cap} chapter title from the structure above)`;
      tone_note = ` (appropriate for ${lang_cap} readers)`;
    }

    emitGraphStatus(
      "WriteChapters",
      progressPercentage + 2,
      `Building prompt for chapter ${chapter_num}`
    );

    const prompt = buildPrompt(WRITE_CHAPTER_PROMPT, {
      language_instruction,
      project_name,
      abstraction_name,
      chapter_num,
      concept_details_note,
      abstraction_description,
      structure_note,
      full_chapter_listing: item.full_chapter_listing,
      prev_summary_note,
      previous_chapters_summary: previous_chapters_summary || "This is the first chapter.",
      file_context_str: file_context_str || "No specific code snippets provided for this abstraction.",
      language_cap: language.charAt(0).toUpperCase() + language.slice(1),
      instruction_lang_note,
      link_lang_note,
      code_comment_note,
      mermaid_lang_note,
      tone_note
    });

 
    emitGraphStatus(
      "WriteChapters",
      progressPercentage + 3,
      `Sending request to LLM for chapter ${chapter_num}`
    );

    const chapter_content = await callLlm_openrouter({
      prompt,
      temperature: 0.7,
      model: "google/gemini-2.5-flash-preview-05-20",
      use_cache: use_cache && this.curRetry === 0,
      user_id: this.user_id
    }
   
    );
  
    emitGraphStatus(
      "WriteChapters",
      progressPercentage + 4,
      `Received response from LLM for chapter ${chapter_num}, processing content`
    );


    // Basic validation/cleanup
    const actual_heading = `# Chapter ${chapter_num}: ${abstraction_name}`;
    let final_content = chapter_content;

    if (!chapter_content.trim().startsWith(`# Chapter ${chapter_num}`)) {
      // Add heading if missing or incorrect
      const lines = chapter_content.trim().split("\n");
      if (lines.length > 0 && lines[0].trim().startsWith("#")) {
        // If there's some heading, replace it
        lines[0] = actual_heading;
        final_content = lines.join("\n");
      } else {
        // Otherwise, prepend it
        final_content = `${actual_heading}\n\n${chapter_content}`;
      }
    }

    // Add the generated content to our temporary list for the next iteration's context
    this.chapters_written_so_far.push(final_content);

    emitGraphStatus(
      "WriteChapters",
      progressPercentage + 5,
      `Completed chapter ${chapter_num}: ${abstraction_name}`
    );

    // Emit progress event for overall progress
    const overallProgress = Math.floor(
      70 + (chapter_num / Object.keys(item.chapter_filenames).length) * 20
    );
    emitProgress(
      "Chapter Writing",
      overallProgress,
      `Writing chapter ${chapter_num} of ${Object.keys(item.chapter_filenames).length}`
    );

    return final_content;
  }

  async post(
    shared: SharedStore,
    _input: unknown,
    exec_res_list: string[]
  ): Promise<string | undefined> {
    emitGraphStatus(
      "WriteChapters",
      95,
      `Storing ${exec_res_list.length} chapters in shared store`
    );
    shared.chapters = exec_res_list;

    // Emit progress event
    emitProgress(
      "Chapter Writing",
      90,
      `Completed writing ${exec_res_list.length} chapters`
    );

    // Final graph status
    emitGraphStatus("WriteChapters", 100, "Chapter writing complete");

    
    return "default";
  }
}
