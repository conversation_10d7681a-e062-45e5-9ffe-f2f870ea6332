
import { createClient } from "@supabase/supabase-js";
import type { Database } from "@/integrations/supabase/types";

const SUPABASE_URL = "https://axdtrqmggulirxskvwjg.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF4ZHRycW1nZ3VsaXJ4c2t2d2pnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY4MTQ2MzEsImV4cCI6MjA2MjM5MDYzMX0.K6tH_zUWz3gFOB9WStMmfDQY8y_jjyi9d-HB4tmSzho";

// Create a static client for analytics
const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_ANON_KEY);

export interface UsageStats {
  totalCalls: number;
  totalCost: number;
  totalTokens: number;
  avgCostPerCall: number;
  costByModel: Record<string, number>;
  callsByModel: Record<string, number>;
}

/**
 * Get usage statistics from the database
 * @param userId Optional user ID to filter by specific user (must be UUID)
 * @param days Number of days to look back (default: 30)
 * @returns Usage statistics
 */
export async function getUsageStats(
  userId?: string,
  days: number = 30
): Promise<UsageStats> {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  let query = supabase
    .from('openrouter_usage')
    .select('*')
    .gte('created_at', startDate.toISOString());

  // Only filter by user_id if it's provided and appears to be a valid UUID
  if (userId && userId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
    query = query.eq('user_id', userId);
  }

  const { data, error } = await query;

  if (error) {
    console.error('Error fetching usage stats:', error);
    throw error;
  }

  const stats: UsageStats = {
    totalCalls: data.length,
    totalCost: 0,
    totalTokens: 0,
    avgCostPerCall: 0,
    costByModel: {},
    callsByModel: {},
  };

  data.forEach((record) => {
    const cost = record.cost || 0;
    const tokens = record.total_tokens || 0;
    const model = record.model;

    stats.totalCost += cost;
    stats.totalTokens += tokens;

    if (!stats.costByModel[model]) {
      stats.costByModel[model] = 0;
      stats.callsByModel[model] = 0;
    }

    stats.costByModel[model] += cost;
    stats.callsByModel[model] += 1;
  });

  stats.avgCostPerCall = stats.totalCalls > 0 ? stats.totalCost / stats.totalCalls : 0;

  return stats;
}

/**
 * Get recent usage data
 * @param limit Number of records to fetch
 * @param userId Optional user ID to filter by (must be UUID)
 * @returns Recent usage records
 */
export async function getRecentUsage(limit: number = 50, userId?: string) {
  let query = supabase
    .from('openrouter_usage')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(limit);

  // Only filter by user_id if it's provided and appears to be a valid UUID
  if (userId && userId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
    query = query.eq('user_id', userId);
  }

  const { data, error } = await query;

  if (error) {
    console.error('Error fetching recent usage:', error);
    throw error;
  }

  return data;
}

/**
 * Calculate estimated cost for a prompt
 * @param prompt The prompt text
 * @param model The model to use
 * @returns Estimated cost in USD
 */
export function estimatePromptCost(prompt: string, model: string): number {
  // Rough token estimation (4 characters per token average)
  const estimatedTokens = Math.ceil(prompt.length / 4);
  
  // Model pricing (per 1K tokens) - these are approximate
  const modelPricing: Record<string, { input: number; output: number }> = {
    "google/gemini-2.5-flash-preview-05-20": { input: 0.000075, output: 0.0003 },
    "openai/gpt-4o": { input: 0.005, output: 0.015 },
    "openai/gpt-4o-mini": { input: 0.00015, output: 0.0006 },
    "anthropic/claude-3.5-sonnet": { input: 0.003, output: 0.015 },
  };

  const pricing = modelPricing[model] || { input: 0.001, output: 0.002 }; // Default pricing
  
  // Estimate input cost + approximate output cost (assuming 1:1 ratio)
  const inputCost = (estimatedTokens / 1000) * pricing.input;
  const outputCost = (estimatedTokens / 1000) * pricing.output;
  
  return inputCost + outputCost;
}
