import yaml from 'js-yaml';
import { Node } from '../../../pocketflow'
import { Abstraction, SharedStore } from '../types';

import { emitGraphStatus, emitProgress } from "../utils/events";
import { callLlm_openrouter } from '@/Agents/shared/callLlm_openrouter';
import { buildPrompt } from "../../../pocketflow/utils/buildPrompt";
import { IDENTIFY_ABSTRACTIONS_PROMPT } from "../prompts/identifyAbstractions";

export class IdentifyAbstractions extends Node<SharedStore> {


  async prep(shared: SharedStore) {
    // Emit graph status to indicate this node is starting
    emitGraphStatus("IdentifyAbstractions", 0, "Starting abstraction identification");

    const files_data = shared.files ?? [];
    const project_name = shared.project_name!;
    const language = shared.language ?? 'english';
    const use_cache = shared.use_cache ?? true;
    const max_abstraction_num = shared.max_abstraction_num ?? 10;

    // Build context string and file index list
    let context = '';

    const temp_file_listing_for_prompt: [number, string][] = [];

    emitGraphStatus("IdentifyAbstractions", 10, `Processing ${files_data.length} files for abstraction analysis`);

    files_data.forEach(([path, content], i) => {
      context += `--- File Index ${i}: ${path} ---\n${content}\n\n`;
      temp_file_listing_for_prompt.push([i, path]);
    });

    emitGraphStatus("IdentifyAbstractions", 20, "Preparing file listing for analysis");

    const file_listing_for_prompt = temp_file_listing_for_prompt
      .map(([idx, p]) => `- ${idx} # ${p}`)
      .join('\n');

    emitGraphStatus("IdentifyAbstractions", 30, "Preparation complete, ready for abstraction analysis");

    return {
              context,
              file_listing_for_prompt,
              file_count: files_data.length,
              project_name,
              language,
              use_cache,
              max_abstraction_num,
              user_id: shared.user_id,
              session_id: shared.session_id
            };
  }

  async exec(prepRes: any) {
    const { context, file_listing_for_prompt, file_count, project_name, language, useCache, maxAbstractions,user_id } = prepRes;

    emitGraphStatus("IdentifyAbstractions", 40, "Starting LLM analysis to identify core abstractions");
    console.log('Identifying abstractions using LLM...');

    let language_instruction = '';
    let name_lang_hint = '';
    let desc_lang_hint = '';

    if (language.toLowerCase() !== 'english') {
      const capLang = language.charAt(0).toUpperCase() + language.slice(1);
      language_instruction = `IMPORTANT: Generate the \`name\` and \`description\` in **${capLang}** language. Do NOT use English for these fields.\n\n`;
      //Keep specific hints here as name/description are primary targets
      name_lang_hint = ` (in ${capLang})`;
      desc_lang_hint = ` (in ${capLang})`;
    }

    const prompt = buildPrompt(IDENTIFY_ABSTRACTIONS_PROMPT, {
      project_name,
      context,
      language_instruction,
      max_abstractions: maxAbstractions,
      name_lang_hint,
      desc_lang_hint,
      file_listing_for_prompt
    });

    emitGraphStatus("IdentifyAbstractions", 50, "Sending request to LLM for abstraction identification");
    const response = await callLlm_openrouter ({prompt, use_cache: useCache, user_id});
    emitGraphStatus("IdentifyAbstractions", 60, "Received response from LLM, processing results");

    //--- Validation ---
    // Extract YAML between ```yaml
    //yaml_str = response.strip().split("```yaml")[1].split("```")[0].strip()

    const yaml_match = response.match(/```yaml([\s\S]*?)```/); // extract the content between ```yaml and ```
    const yaml_str = yaml_match ? yaml_match[1] : response;    // if not found, use the entire response
   // console.log('YAML Str:', yaml_str);
    if (!yaml_str) {
      emitGraphStatus("IdentifyAbstractions", 65, "Error: LLM did not return YAML block");
      throw new Error('LLM did not return YAML block');
    }

    emitGraphStatus("IdentifyAbstractions", 70, "Parsing YAML response");
    const abstractions = yaml.load(yaml_str) as any;

    console.log('Abstractions:', abstractions);
    if (!Array.isArray(abstractions)) {
      emitGraphStatus("IdentifyAbstractions", 75, "Error: LLM output is not a list");
      throw new Error('LLM Output is not a list');
    }

    emitGraphStatus("IdentifyAbstractions", 80, `Found ${abstractions.length} core abstractions`);



    // Validate and transform
    emitGraphStatus("IdentifyAbstractions", 85, "Validating and transforming abstractions");

    try {
      const result = abstractions.map((item: any) => {
        if (typeof item.name !== 'string' || typeof item.description !== 'string' || !Array.isArray(item.file_indices)) {
          emitGraphStatus("IdentifyAbstractions", 87, `Error: Invalid abstraction format: ${JSON.stringify(item)}`);
          throw new Error(`Invalid abstraction format: ${JSON.stringify(item)}`);
        }
      //  console.log('Abstraction:', item);

        // parse indices
        const validatedIndices: number[] = [];

        for (const entry of item.file_indices) {
          try {
            let idx: number;

            if (typeof entry === 'number') {
              idx = entry;
            } else if (typeof entry === 'string' && entry.includes('#')) {
              idx = parseInt(entry.split('#')[0].trim(), 10);
            } else {
              idx = parseInt(String(entry).trim(), 10);
            }

            if (isNaN(idx) || !(0 <= idx && idx < file_count)) {
              emitGraphStatus("IdentifyAbstractions", 88, `Error: Invalid file index ${idx} found in item ${item.name}`);
              throw new Error(`Invalid file index ${idx} found in item ${item.name}. Max index is ${file_count - 1}.`);
            }

            validatedIndices.push(idx);
          } catch (error) {
            emitGraphStatus("IdentifyAbstractions", 89, `Error: Could not parse index from entry: ${entry} in item ${item.name}`);
            throw new Error(`Could not parse index from entry: ${entry} in item ${item.name}`);
          }
        }

        // Remove duplicates and sort
        const files = [...new Set(validatedIndices)].sort((a, b) => a - b);

        return {
          name: item.name,
          description: item.description,
          files
        };
      }) as Abstraction[];

      emitGraphStatus("IdentifyAbstractions", 90, "Successfully validated all abstractions");
      return result;
    } catch (error) {
      emitGraphStatus("IdentifyAbstractions", 90, `Error during abstraction validation: ${error.message}`);
      throw error;
    }
  }

  async post(shared: SharedStore, _prep: any, execRes: Abstraction[]) {
    emitGraphStatus("IdentifyAbstractions", 95, `Storing ${execRes.length} abstractions in shared store`);
    // console.log('Identified abstractions:', execRes);
    shared.abstractions = execRes; // # List of {"name": str, "description": str, "files": [int]}

    // Emit progress event
    emitProgress("Abstraction Identification", 40, `Identified ${execRes.length} core abstractions`);

    // Final graph status
    emitGraphStatus("IdentifyAbstractions", 100, "Abstraction identification complete");

    return undefined;
  }
}
