
import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Switch } from "@/components/ui/switch";
import { FileText, Eye } from "lucide-react";
import MarkdownRenderer from "./MarkdownRenderer";

export type Chapter = {
  id: string;
  title: string;
  content: string;
};

export type TutorialContent = {
  id: string;
  title: string;
  description: string;
  repoUrl: string;
  chapters: Chapter[];
};

type TutorialViewerProps = {
  tutorial: TutorialContent;
};

const TutorialViewer = ({ tutorial }: TutorialViewerProps) => {
  // Default to project overview (first chapter) when available
  const [activeChapter, setActiveChapter] = useState<string>(tutorial.chapters[0]?.id || "");
  const [showMarkdownSource, setShowMarkdownSource] = useState(false);

  // Extract mermaid diagrams from chapter content
  const extractMermaidDiagrams = (content: string): string[] => {
    const mermaidRegex = /```mermaid([\s\S]*?)```/g;
    const matches = [...content.matchAll(mermaidRegex)];
    return matches.map(match => match[1].trim());
  };

  // Extract code blocks (excluding mermaid) from chapter content
  const extractCodeBlocks = (content: string): { language: string, code: string }[] => {
    const codeBlockRegex = /```([\w-]*)\n([\s\S]*?)```/g;
    const matches = [...content.matchAll(codeBlockRegex)];
    return matches
      .filter(match => match[1] !== 'mermaid')
      .map(match => ({
        language: match[1].trim() || 'plaintext',
        code: match[2].trim()
      }));
  };

  const activeChapterContent = tutorial.chapters.find(chapter => chapter.id === activeChapter)?.content || "";
  const mermaidDiagrams = extractMermaidDiagrams(activeChapterContent);
  const codeBlocks = extractCodeBlocks(activeChapterContent);

  return (
    <div className="flex flex-col md:flex-row w-full gap-6 animate-fade-in">
      <Card className="md:w-64 flex-shrink-0">
        <CardContent className="p-4">
          <h3 className="font-medium mb-4">Table of Contents</h3>
          <ScrollArea className="h-[calc(100vh-200px)] overflow-x-hidden">
            <ul className="space-y-1 w-full">
              {tutorial.chapters.map((chapter, index) => {
                // Calculate the correct chapter number (skip counting project_overview)
                const chapterNumber = chapter.id === "project_overview" ? null :
                  tutorial.chapters.findIndex(ch => ch.id === "project_overview") === 0 ?
                    index : index + 1;

                return (
                  <li key={chapter.id} className="overflow-hidden">
                    <button
                      onClick={() => setActiveChapter(chapter.id)}
                      className={`w-full text-left px-3 py-2 text-sm rounded-md transition-colors truncate ${
                        activeChapter === chapter.id
                          ? "bg-tutorial-light text-tutorial-primary font-medium"
                          : "hover:bg-gray-100"
                      }`}
                      title={chapter.title}
                    >
                      {chapterNumber ? `${chapterNumber}. ${chapter.title}` : chapter.title}
                    </button>
                  </li>
                );
              })}
            </ul>
          </ScrollArea>
        </CardContent>
      </Card>

      <Card className="flex-1 w-full overflow-hidden">
        <CardContent className="p-6 overflow-hidden">
          <Tabs defaultValue="content" className="w-full">
            <div className="flex justify-between items-center mb-4">
              <TabsList>
                <TabsTrigger value="content">Content</TabsTrigger>
                <TabsTrigger value="diagrams" disabled={mermaidDiagrams.length === 0}>Diagrams</TabsTrigger>
                <TabsTrigger value="code" disabled={codeBlocks.length === 0}>Code Examples</TabsTrigger>
              </TabsList>
              <div className="flex items-center space-x-2 bg-white dark:bg-gray-800 p-1.5 rounded-md shadow-sm border border-gray-200 dark:border-gray-700">
                <Eye className={`h-4 w-4 ${!showMarkdownSource ? 'text-tutorial-primary' : 'text-gray-400'}`} />
                <Switch
                  id="markdown-toggle"
                  checked={showMarkdownSource}
                  onCheckedChange={setShowMarkdownSource}
                />
                <FileText className={`h-4 w-4 ${showMarkdownSource ? 'text-tutorial-primary' : 'text-gray-400'}`} />
              </div>
            </div>
            <TabsContent value="content" className="mt-0">
              <ScrollArea className="h-[calc(100vh-200px)] pr-4 overflow-x-hidden w-full">
                <div className="w-full max-w-full overflow-hidden">
                  {tutorial.chapters.map((chapter) => (
                    <div
                      key={chapter.id}
                      className={activeChapter === chapter.id ? "block w-full overflow-hidden" : "hidden"}
                      style={{ maxWidth: '100%' }}
                    >
                      <MarkdownRenderer content={chapter.content} showMarkdownSource={showMarkdownSource} />
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
            <TabsContent value="diagrams" className="mt-0">
              <ScrollArea className="h-[calc(100vh-200px)] pr-4 overflow-x-hidden">
                <div className={activeChapter ? "block w-full overflow-hidden" : "hidden"}>
                  <h2 className="text-2xl font-bold mb-4">
                    Diagrams from {tutorial.chapters.find(ch => ch.id === activeChapter)?.title}
                  </h2>
                  {mermaidDiagrams.length > 0 ? (
                    <div className="space-y-8">
                      {mermaidDiagrams.map((diagram, i) => (
                        <div key={`diagram-${i}`} className="p-4 bg-gray-50 rounded-lg overflow-hidden">
                          <MarkdownRenderer content={`\`\`\`mermaid\n${diagram}\n\`\`\``} showMarkdownSource={showMarkdownSource} />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No diagrams found in this chapter</p>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>
            <TabsContent value="code" className="mt-0">
              <ScrollArea className="h-[calc(100vh-200px)] pr-4 overflow-x-hidden">
                <div className={activeChapter ? "block w-full overflow-hidden" : "hidden"}>
                  <h2 className="text-2xl font-bold mb-4">
                    Code Examples from {tutorial.chapters.find(ch => ch.id === activeChapter)?.title}
                  </h2>
                  {codeBlocks.length > 0 ? (
                    <div className="space-y-6">
                      {codeBlocks.map((block, i) => (
                        <div key={`code-block-${i}`} className="rounded-lg overflow-hidden">
                          <div className="bg-gray-800 text-gray-200 px-4 py-1 text-sm">{block.language}</div>
                          <div className="overflow-x-auto">
                            <pre className="bg-gray-900 text-gray-100 p-4">
                              <code>{block.code}</code>
                            </pre>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No code examples found in this chapter</p>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default TutorialViewer;
